package com.stpl.tech.pivot.domain;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RefundRequest {
    
    @NotNull(message = "Order ID cannot be null")
    private String orderId;
    
    @NotNull(message = "Transaction ID cannot be null")
    private String transactionId;
    
    @NotNull(message = "Refund ID cannot be null")
    private String refundId;
    
    @NotNull(message = "Refund Amount cannot be null")
    private BigDecimal refundAmount;
    
    @NotNull(message = "Payment Partner Type cannot be null")
    private PaymentPartnerType paymentPartnerType;
    
    private String refundReason;
    
    private String comments;
    
    // Additional fields that might be needed for different payment partners
    private String merchantId;
    private String terminalId;
    private String storeCode;
}
