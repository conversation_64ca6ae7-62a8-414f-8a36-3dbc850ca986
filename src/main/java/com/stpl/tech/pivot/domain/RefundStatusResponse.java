package com.stpl.tech.pivot.domain;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RefundStatusResponse {
    
    private String orderId;
    
    private String transactionId;
    
    private String refundId;
    
    private String partnerRefundId;
    
    private BigDecimal refundAmount;
    
    private BigDecimal totalRefundAmount;
    
    private String status;
    
    private String statusMessage;
    
    private PaymentPartnerType paymentPartnerType;
    
    private LocalDateTime refundTimestamp;
    
    private LocalDateTime acceptRefundTimestamp;
    
    private String resultCode;
    
    private String resultMessage;
    
    private boolean success;
    
    private String acceptRefundStatus;
    
    private String userCreditInitiateStatus;
    
    private LocalDateTime userCreditInitiateTimestamp;
    
    private String refundReason;
    
    // Additional response fields
    private String merchantId;
    private String signature;
    private String responseTimestamp;
    
    // For detailed refund information
    private List<RefundDetailInfo> refundDetailInfoList;
    private AgentInfo agentInfo;
}
