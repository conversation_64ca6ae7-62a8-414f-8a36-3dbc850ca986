package com.stpl.tech.pivot.domain;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RefundResponse {
    
    private String orderId;
    
    private String transactionId;
    
    private String refundId;
    
    private String partnerRefundId;
    
    private BigDecimal refundAmount;
    
    private String status;
    
    private String statusMessage;
    
    private PaymentPartnerType paymentPartnerType;
    
    private LocalDateTime refundTimestamp;
    
    private String resultCode;
    
    private String resultMessage;
    
    private boolean success;
    
    // Additional response fields
    private String merchantId;
    private String signature;
    private String responseTimestamp;
}
