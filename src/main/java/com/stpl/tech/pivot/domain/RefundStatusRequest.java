package com.stpl.tech.pivot.domain;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RefundStatusRequest {
    
    @NotNull(message = "Order ID cannot be null")
    private String orderId;
    
    @NotNull(message = "Refund ID cannot be null")
    private String refundId;
    
    @NotNull(message = "Payment Partner Type cannot be null")
    private PaymentPartnerType paymentPartnerType;
    
    // Additional fields that might be needed for different payment partners
    private String merchantId;
    private String transactionId;
    private String terminalId;
    private String storeCode;
}
