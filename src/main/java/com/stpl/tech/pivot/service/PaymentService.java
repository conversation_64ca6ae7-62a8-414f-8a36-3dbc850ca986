package com.stpl.tech.pivot.service;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.pivot.domain.PaytmEDCStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusResponse;

import java.util.Map;
import com.stpl.tech.pivot.domain.RefundRequest;
import com.stpl.tech.pivot.domain.RefundResponse;
import com.stpl.tech.pivot.domain.RefundStatusRequest;
import com.stpl.tech.pivot.domain.RefundStatusResponse;

public interface PaymentService {

    PaytmEdcCreateRequest initiateTransactionRequest(OrderPaymentRequest order, PaymentPartnerType patymEdc) throws Exception;

    OrderPaymentDetail validateOrderPaymentDetail(String transactionId);

    PaytmEDCStatusResponse updateTransactionRequest(PaytmEdcStatusRequest request, PaymentPartnerType patymEdc) throws Exception;

    PayPhiEdcCreateRequest initiatePayphiTransactionRequest(OrderPaymentRequest order, PaymentPartnerType patymEdc) throws Exception;

    PayPhiStatusRequest updatePayPhiTransactionRequest(PayPhiStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception;

    PaytmDQRCreateRequest initiateDqrTransactionRequest(OrderPaymentRequest order, PaymentPartnerType patymDqr) throws Exception;
    PaytmDQRStatusResponse updateDqrTransactionRequest(PaytmDQRStatusRequest request, PaymentPartnerType paytmDqr) throws Exception;
    void updateTransactionStatus(Object response) throws Exception;

    RefundResponse refundPayment(RefundRequest refundRequest) throws Exception;

    RefundStatusResponse getRefundStatus(RefundStatusRequest refundStatusRequest) throws Exception;

}
