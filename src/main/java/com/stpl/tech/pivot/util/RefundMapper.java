package com.stpl.tech.pivot.util;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.*;
import com.stpl.tech.pivot.properties.PaytmProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class RefundMapper {

    @Autowired
    private PaytmProperties paytmProperties;

    /**
     * Convert generic RefundRequest to PaytmRefundRequest
     */
    public PaytmRefundRequest toPaytmRefundRequest(RefundRequest refundRequest) {
        return PaytmRefundRequest.builder()
                .mid(refundRequest.getMerchantId() != null ? refundRequest.getMerchantId() : paytmProperties.getMerchantId())
                .txnType("REFUND")
                .orderId(refundRequest.getOrderId())
                .txnId(refundRequest.getTransactionId())
                .refId(refundRequest.getRefundId())
                .refundAmount(refundRequest.getRefundAmount())
                .merchantKey(paytmProperties.getMerchantKey())
                .paymentPartnerType(refundRequest.getPaymentPartnerType())
                .build();
    }

    /**
     * Convert PaytmRefundResponse to generic RefundResponse
     */
    public RefundResponse toGenericRefundResponse(PaytmRefundResponse paytmResponse, PaymentPartnerType paymentPartnerType) {
        if (paytmResponse == null || paytmResponse.getBody() == null) {
            return RefundResponse.builder()
                    .success(false)
                    .statusMessage("No response received from payment partner")
                    .paymentPartnerType(paymentPartnerType)
                    .build();
        }

        PaytmRefundResponseBody body = paytmResponse.getBody();
        PaytmRefundResponseHeader head = paytmResponse.getHead();

        return RefundResponse.builder()
                .orderId(body.getOrderId())
                .transactionId(body.getTxnId())
                .refundId(body.getRefId())
                .partnerRefundId(body.getRefundId())
                .refundAmount(body.getRefundAmount() != null ? new java.math.BigDecimal(body.getRefundAmount()) : null)
                .status(body.getResultInfo() != null ? body.getResultInfo().getResultStatus() : null)
                .statusMessage(body.getResultInfo() != null ? body.getResultInfo().getResultMsg() : null)
                .resultCode(body.getResultInfo() != null ? body.getResultInfo().getResultCode() : null)
                .resultMessage(body.getResultInfo() != null ? body.getResultInfo().getResultMsg() : null)
                .success(body.getResultInfo() != null && "TXN_SUCCESS".equalsIgnoreCase(body.getResultInfo().getResultStatus()))
                .paymentPartnerType(paymentPartnerType)
                .merchantId(body.getMid())
                .signature(head != null ? head.getSignature() : null)
                .responseTimestamp(head != null ? head.getResponseTimestamp() : null)
                .refundTimestamp(parseTimestamp(body.getTxnTimestamp()))
                .build();
    }

    /**
     * Convert generic RefundStatusRequest to PaytmRefundStatusRequest
     */
    public PaytmRefundStatusRequest toPaytmRefundStatusRequest(RefundStatusRequest refundStatusRequest) {
        return PaytmRefundStatusRequest.builder()
                .mid(refundStatusRequest.getMerchantId() != null ? refundStatusRequest.getMerchantId() : paytmProperties.getMerchantId())
                .orderId(refundStatusRequest.getOrderId())
                .refId(refundStatusRequest.getRefundId())
                .merchantKey(paytmProperties.getMerchantKey())
                .paymentPartnerType(refundStatusRequest.getPaymentPartnerType())
                .build();
    }

    /**
     * Convert PaytmRefundStatusResponse to generic RefundStatusResponse
     */
    public RefundStatusResponse toGenericRefundStatusResponse(PaytmRefundStatusResponse paytmResponse, PaymentPartnerType paymentPartnerType) {
        if (paytmResponse == null || paytmResponse.getBody() == null) {
            return RefundStatusResponse.builder()
                    .success(false)
                    .statusMessage("No response received from payment partner")
                    .paymentPartnerType(paymentPartnerType)
                    .build();
        }

        PaytmRefundStatusResponseBody body = paytmResponse.getBody();
        PaytmRefundStatusResponseHeader head = paytmResponse.getHead();

        return RefundStatusResponse.builder()
                .orderId(body.getOrderId())
                .transactionId(body.getTxnId())
                .refundId(body.getRefId())
                .partnerRefundId(body.getRefundId())
                .refundAmount(body.getRefundAmount() != null ? new java.math.BigDecimal(body.getRefundAmount()) : null)
                .totalRefundAmount(body.getTotalRefundAmount() != null ? new java.math.BigDecimal(body.getTotalRefundAmount()) : null)
                .status(body.getResultInfo() != null ? body.getResultInfo().getResultStatus() : null)
                .statusMessage(body.getResultInfo() != null ? body.getResultInfo().getResultMsg() : null)
                .resultCode(body.getResultInfo() != null ? body.getResultInfo().getResultCode() : null)
                .resultMessage(body.getResultInfo() != null ? body.getResultInfo().getResultMsg() : null)
                .success(body.getResultInfo() != null && "TXN_SUCCESS".equalsIgnoreCase(body.getResultInfo().getResultStatus()))
                .paymentPartnerType(paymentPartnerType)
                .acceptRefundStatus(body.getAcceptRefundStatus())
                .userCreditInitiateStatus(body.getUserCreditInitiateStatus())
                .refundReason(body.getRefundReason())
                .merchantId(body.getMid())
                .signature(head != null ? head.getSignature() : null)
                .responseTimestamp(head != null ? head.getResponseTimestamp() : null)
                .refundTimestamp(parseTimestamp(body.getTxnTimestamp()))
                .acceptRefundTimestamp(parseTimestamp(body.getAcceptRefundTimestamp()))
                .userCreditInitiateTimestamp(parseTimestamp(body.getUserCreditInitiateTimestamp()))
                .refundDetailInfoList(body.getRefundDetailInfoList())
                .agentInfo(body.getAgentInfo())
                .build();
    }

    /**
     * Parse timestamp string to LocalDateTime
     */
    private LocalDateTime parseTimestamp(String timestamp) {
        if (timestamp == null || timestamp.trim().isEmpty()) {
            return null;
        }
        try {
            // Adjust the pattern based on the actual timestamp format from Paytm
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
            return LocalDateTime.parse(timestamp, formatter);
        } catch (Exception e) {
            // If parsing fails, return null or current time based on requirements
            return null;
        }
    }
}
